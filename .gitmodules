[submodule "thirdparty/glfw"]
	path = thirdparty/glfw
	url = https://github.com/glfw/glfw.git
[submodule "thirdparty/unirender"]
	path = thirdparty/unirender
	url = https://github.com/xzrunner/unirender.git
[submodule "thirdparty/shadertrans"]
	path = thirdparty/shadertrans
	url = https://github.com/xzrunner/shadertrans.git
[submodule "thirdparty/vessel"]
	path = thirdparty/vessel
	url = https://github.com/xzrunner/vessel.git
[submodule "thirdparty/tessellation"]
	path = thirdparty/tessellation
	url = https://github.com/xzrunner/tessellation.git
[submodule "thirdparty/sm"]
	path = thirdparty/sm
	url = https://github.com/xzrunner/sm.git
[submodule "thirdparty/primitive"]
	path = thirdparty/primitive
	url = https://github.com/xzrunner/primitive.git
[submodule "thirdparty/gtxt"]
	path = thirdparty/gtxt
	url = https://github.com/xzrunner/gtxt.git
[submodule "thirdparty/dtex"]
	path = thirdparty/dtex
	url = https://github.com/xzrunner/dtex.git
[submodule "thirdparty/freetype"]
	path = thirdparty/freetype
	url = https://github.com/freetype/freetype.git
[submodule "thirdparty/fs"]
	path = thirdparty/fs
	url = https://github.com/xzrunner/fs.git
[submodule "thirdparty/ds"]
	path = thirdparty/ds
	url = https://github.com/xzrunner/ds.git
[submodule "thirdparty/texpack"]
	path = thirdparty/texpack
	url = https://github.com/xzrunner/texpack.git
[submodule "thirdparty/geoshape"]
	path = thirdparty/geoshape
	url = https://github.com/xzrunner/geoshape.git
[submodule "thirdparty/easygui"]
	path = thirdparty/easygui
	url = https://github.com/xzrunner/easygui.git
[submodule "thirdparty/gimg"]
	path = thirdparty/gimg
	url = https://github.com/xzrunner/gimg.git
[submodule "thirdparty/logger"]
	path = thirdparty/logger
	url = https://github.com/xzrunner/logger.git
[submodule "thirdparty/rg-etc1"]
	path = thirdparty/rg-etc1
	url = https://github.com/richgel999/rg-etc1.git
[submodule "thirdparty/zlib"]
	path = thirdparty/zlib
	url = https://github.com/madler/zlib.git
[submodule "thirdparty/libjpeg-turbo"]
	path = thirdparty/libjpeg-turbo
	url = https://github.com/libjpeg-turbo/libjpeg-turbo.git
[submodule "thirdparty/libpng"]
	path = thirdparty/libpng
	url = https://github.com/glennrp/libpng.git
[submodule "thirdparty/model"]
	path = thirdparty/model
	url = https://github.com/xzrunner/model.git
[submodule "thirdparty/assimp"]
	path = thirdparty/assimp
	url = https://github.com/assimp/assimp.git
[submodule "thirdparty/tinyobjloader"]
	path = thirdparty/tinyobjloader
	url = https://github.com/tinyobjloader/tinyobjloader.git
[submodule "thirdparty/guard"]
	path = thirdparty/guard
	url = https://github.com/xzrunner/guard.git
[submodule "thirdparty/polymesh3"]
	path = thirdparty/polymesh3
	url = https://github.com/xzrunner/polymesh3.git
[submodule "thirdparty/halfedge"]
	path = thirdparty/halfedge
	url = https://github.com/xzrunner/halfedge.git
[submodule "thirdparty/shaderlink"]
	path = thirdparty/shaderlink
	url = https://github.com/xzrunner/shaderlink.git
[submodule "thirdparty/uniphysics"]
	path = thirdparty/uniphysics
	url = https://github.com/xzrunner/uniphysics.git
[submodule "thirdparty/eigen"]
	path = thirdparty/eigen
	url = https://gitlab.com/libeigen/eigen.git
[submodule "thirdparty/heightfield"]
	path = thirdparty/heightfield
	url = https://github.com/xzrunner/heightfield.git
[submodule "thirdparty/nurbs"]
	path = thirdparty/nurbs
	url = https://github.com/xzrunner/nurbs.git
[submodule "thirdparty/lexer"]
	path = thirdparty/lexer
	url = https://github.com/xzrunner/lexer.git
[submodule "thirdparty/brepdb"]
	path = thirdparty/brepdb
	url = https://github.com/xzrunner/brepdb.git
[submodule "thirdparty/cslang"]
	path = thirdparty/cslang
	url = https://github.com/xzrunner/cslang.git
[submodule "thirdparty/catch2"]
	path = thirdparty/catch2
	url = https://github.com/catchorg/Catch2.git
[submodule "thirdparty/easyvm"]
	path = thirdparty/easyvm
	url = https://github.com/xzrunner/easyvm.git
[submodule "thirdparty/brepvm"]
	path = thirdparty/brepvm
	url = https://github.com/xzrunner/brepvm.git
[submodule "thirdparty/brepom"]
	path = thirdparty/brepom
	url = https://github.com/xzrunner/brepom.git
[submodule "thirdparty/graph"]
	path = thirdparty/graph
	url = https://github.com/xzrunner/graph.git
[submodule "thirdparty/objcomp"]
	path = thirdparty/objcomp
	url = https://github.com/xzrunner/objcomp.git
