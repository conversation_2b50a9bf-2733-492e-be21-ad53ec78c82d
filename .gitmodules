[submodule "thirdparty/glfw"]
	path = thirdparty/glfw
	url = **************:glfw/glfw.git
[submodule "thirdparty/unirender"]
	path = thirdparty/unirender
	url = **************:xzrunner/unirender.git
[submodule "thirdparty/shadertrans"]
	path = thirdparty/shadertrans
	url = **************:xzrunner/shadertrans.git
[submodule "thirdparty/vessel"]
	path = thirdparty/vessel
	url = **************:xzrunner/vessel.git
[submodule "thirdparty/tessellation"]
	path = thirdparty/tessellation
	url = **************:xzrunner/tessellation.git
[submodule "thirdparty/sm"]
	path = thirdparty/sm
	url = **************:xzrunner/sm.git
[submodule "thirdparty/primitive"]
	path = thirdparty/primitive
	url = **************:xzrunner/primitive.git
[submodule "thirdparty/gtxt"]
	path = thirdparty/gtxt
	url = **************:xzrunner/gtxt.git
[submodule "thirdparty/dtex"]
	path = thirdparty/dtex
	url = **************:xzrunner/dtex.git
[submodule "thirdparty/freetype"]
	path = thirdparty/freetype
	url = **************:freetype/freetype.git
[submodule "thirdparty/fs"]
	path = thirdparty/fs
	url = **************:xzrunner/fs.git
[submodule "thirdparty/ds"]
	path = thirdparty/ds
	url = **************:xzrunner/ds.git
[submodule "thirdparty/texpack"]
	path = thirdparty/texpack
	url = **************:xzrunner/texpack.git
[submodule "thirdparty/geoshape"]
	path = thirdparty/geoshape
	url = **************:xzrunner/geoshape.git
[submodule "thirdparty/easygui"]
	path = thirdparty/easygui
	url = **************:xzrunner/easygui.git
[submodule "thirdparty/gimg"]
	path = thirdparty/gimg
	url = **************:xzrunner/gimg.git
[submodule "thirdparty/logger"]
	path = thirdparty/logger
	url = **************:xzrunner/logger.git
[submodule "thirdparty/rg-etc1"]
	path = thirdparty/rg-etc1
	url = **************:richgel999/rg-etc1.git
[submodule "thirdparty/zlib"]
	path = thirdparty/zlib
	url = **************:madler/zlib.git
[submodule "thirdparty/libjpeg-turbo"]
	path = thirdparty/libjpeg-turbo
	url = **************:libjpeg-turbo/libjpeg-turbo.git
[submodule "thirdparty/libpng"]
	path = thirdparty/libpng
	url = **************:glennrp/libpng.git
[submodule "thirdparty/model"]
	path = thirdparty/model
	url = **************:xzrunner/model.git
[submodule "thirdparty/assimp"]
	path = thirdparty/assimp
	url = **************:assimp/assimp.git
[submodule "thirdparty/tinyobjloader"]
	path = thirdparty/tinyobjloader
	url = **************:tinyobjloader/tinyobjloader.git
[submodule "thirdparty/guard"]
	path = thirdparty/guard
	url = **************:xzrunner/guard.git
[submodule "thirdparty/polymesh3"]
	path = thirdparty/polymesh3
	url = **************:xzrunner/polymesh3.git
[submodule "thirdparty/halfedge"]
	path = thirdparty/halfedge
	url = **************:xzrunner/halfedge.git
[submodule "thirdparty/shaderlink"]
	path = thirdparty/shaderlink
	url = **************:xzrunner/shaderlink.git
[submodule "thirdparty/uniphysics"]
	path = thirdparty/uniphysics
	url = **************:xzrunner/uniphysics.git
[submodule "thirdparty/eigen"]
	path = thirdparty/eigen
	url = **************:libeigen/eigen.git
[submodule "thirdparty/heightfield"]
	path = thirdparty/heightfield
	url = **************:xzrunner/heightfield.git
[submodule "thirdparty/nurbs"]
	path = thirdparty/nurbs
	url = **************:xzrunner/nurbs.git
[submodule "thirdparty/lexer"]
	path = thirdparty/lexer
	url = **************:xzrunner/lexer.git
[submodule "thirdparty/brepdb"]
	path = thirdparty/brepdb
	url = **************:xzrunner/brepdb.git
[submodule "thirdparty/cslang"]
	path = thirdparty/cslang
	url = **************:xzrunner/cslang.git
[submodule "thirdparty/catch2"]
	path = thirdparty/catch2
	url = **************:catchorg/Catch2.git
[submodule "thirdparty/easyvm"]
	path = thirdparty/easyvm
	url = **************:xzrunner/easyvm.git
[submodule "thirdparty/brepvm"]
	path = thirdparty/brepvm
	url = **************:xzrunner/brepvm.git
[submodule "thirdparty/brepom"]
	path = thirdparty/brepom
	url = **************:xzrunner/brepom.git
[submodule "thirdparty/graph"]
	path = thirdparty/graph
	url = **************:xzrunner/graph.git
[submodule "thirdparty/objcomp"]
	path = thirdparty/objcomp
	url = **************:xzrunner/objcomp.git
