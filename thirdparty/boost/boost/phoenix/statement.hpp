/*==============================================================================
    Copyright (c) 2001-2010 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#ifndef BOOST_PHOENIX_STATEMENT_HPP
#define BOOST_PHOENIX_STATEMENT_HPP

#include <boost/phoenix/version.hpp>
#include <boost/phoenix/statement/do_while.hpp>
#include <boost/phoenix/statement/for.hpp>
#include <boost/phoenix/statement/if.hpp>
#include <boost/phoenix/statement/sequence.hpp>
#include <boost/phoenix/statement/switch.hpp>
#include <boost/phoenix/statement/throw.hpp>
#include <boost/phoenix/statement/try_catch.hpp>
#include <boost/phoenix/statement/while.hpp>

#endif
