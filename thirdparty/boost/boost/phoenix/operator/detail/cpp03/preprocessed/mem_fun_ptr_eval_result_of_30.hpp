/*==============================================================================
    Copyright (c) 2005-2010 <PERSON>
    Copyright (c) 2010 Thomas <PERSON>
    Copyright (c) 2018 <PERSON><PERSON><PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1>
        struct mem_fun_ptr_eval<Context, A0 , A1>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1;
            typedef
                typename boost::result_of<
                    child1(
                        child1
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15; typedef typename evaluator::impl< A16 , Context , proto::empty_env >::result_type child16;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15 , child16
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15; typedef typename evaluator::impl< A16 , Context , proto::empty_env >::result_type child16; typedef typename evaluator::impl< A17 , Context , proto::empty_env >::result_type child17;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15 , child16 , child17
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15; typedef typename evaluator::impl< A16 , Context , proto::empty_env >::result_type child16; typedef typename evaluator::impl< A17 , Context , proto::empty_env >::result_type child17; typedef typename evaluator::impl< A18 , Context , proto::empty_env >::result_type child18;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15 , child16 , child17 , child18
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15; typedef typename evaluator::impl< A16 , Context , proto::empty_env >::result_type child16; typedef typename evaluator::impl< A17 , Context , proto::empty_env >::result_type child17; typedef typename evaluator::impl< A18 , Context , proto::empty_env >::result_type child18; typedef typename evaluator::impl< A19 , Context , proto::empty_env >::result_type child19;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15 , child16 , child17 , child18 , child19
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15; typedef typename evaluator::impl< A16 , Context , proto::empty_env >::result_type child16; typedef typename evaluator::impl< A17 , Context , proto::empty_env >::result_type child17; typedef typename evaluator::impl< A18 , Context , proto::empty_env >::result_type child18; typedef typename evaluator::impl< A19 , Context , proto::empty_env >::result_type child19; typedef typename evaluator::impl< A20 , Context , proto::empty_env >::result_type child20;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15 , child16 , child17 , child18 , child19 , child20
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15; typedef typename evaluator::impl< A16 , Context , proto::empty_env >::result_type child16; typedef typename evaluator::impl< A17 , Context , proto::empty_env >::result_type child17; typedef typename evaluator::impl< A18 , Context , proto::empty_env >::result_type child18; typedef typename evaluator::impl< A19 , Context , proto::empty_env >::result_type child19; typedef typename evaluator::impl< A20 , Context , proto::empty_env >::result_type child20; typedef typename evaluator::impl< A21 , Context , proto::empty_env >::result_type child21;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15 , child16 , child17 , child18 , child19 , child20 , child21
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15; typedef typename evaluator::impl< A16 , Context , proto::empty_env >::result_type child16; typedef typename evaluator::impl< A17 , Context , proto::empty_env >::result_type child17; typedef typename evaluator::impl< A18 , Context , proto::empty_env >::result_type child18; typedef typename evaluator::impl< A19 , Context , proto::empty_env >::result_type child19; typedef typename evaluator::impl< A20 , Context , proto::empty_env >::result_type child20; typedef typename evaluator::impl< A21 , Context , proto::empty_env >::result_type child21; typedef typename evaluator::impl< A22 , Context , proto::empty_env >::result_type child22;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15 , child16 , child17 , child18 , child19 , child20 , child21 , child22
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15; typedef typename evaluator::impl< A16 , Context , proto::empty_env >::result_type child16; typedef typename evaluator::impl< A17 , Context , proto::empty_env >::result_type child17; typedef typename evaluator::impl< A18 , Context , proto::empty_env >::result_type child18; typedef typename evaluator::impl< A19 , Context , proto::empty_env >::result_type child19; typedef typename evaluator::impl< A20 , Context , proto::empty_env >::result_type child20; typedef typename evaluator::impl< A21 , Context , proto::empty_env >::result_type child21; typedef typename evaluator::impl< A22 , Context , proto::empty_env >::result_type child22; typedef typename evaluator::impl< A23 , Context , proto::empty_env >::result_type child23;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15 , child16 , child17 , child18 , child19 , child20 , child21 , child22 , child23
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15; typedef typename evaluator::impl< A16 , Context , proto::empty_env >::result_type child16; typedef typename evaluator::impl< A17 , Context , proto::empty_env >::result_type child17; typedef typename evaluator::impl< A18 , Context , proto::empty_env >::result_type child18; typedef typename evaluator::impl< A19 , Context , proto::empty_env >::result_type child19; typedef typename evaluator::impl< A20 , Context , proto::empty_env >::result_type child20; typedef typename evaluator::impl< A21 , Context , proto::empty_env >::result_type child21; typedef typename evaluator::impl< A22 , Context , proto::empty_env >::result_type child22; typedef typename evaluator::impl< A23 , Context , proto::empty_env >::result_type child23; typedef typename evaluator::impl< A24 , Context , proto::empty_env >::result_type child24;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15 , child16 , child17 , child18 , child19 , child20 , child21 , child22 , child23 , child24
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15; typedef typename evaluator::impl< A16 , Context , proto::empty_env >::result_type child16; typedef typename evaluator::impl< A17 , Context , proto::empty_env >::result_type child17; typedef typename evaluator::impl< A18 , Context , proto::empty_env >::result_type child18; typedef typename evaluator::impl< A19 , Context , proto::empty_env >::result_type child19; typedef typename evaluator::impl< A20 , Context , proto::empty_env >::result_type child20; typedef typename evaluator::impl< A21 , Context , proto::empty_env >::result_type child21; typedef typename evaluator::impl< A22 , Context , proto::empty_env >::result_type child22; typedef typename evaluator::impl< A23 , Context , proto::empty_env >::result_type child23; typedef typename evaluator::impl< A24 , Context , proto::empty_env >::result_type child24; typedef typename evaluator::impl< A25 , Context , proto::empty_env >::result_type child25;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15 , child16 , child17 , child18 , child19 , child20 , child21 , child22 , child23 , child24 , child25
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15; typedef typename evaluator::impl< A16 , Context , proto::empty_env >::result_type child16; typedef typename evaluator::impl< A17 , Context , proto::empty_env >::result_type child17; typedef typename evaluator::impl< A18 , Context , proto::empty_env >::result_type child18; typedef typename evaluator::impl< A19 , Context , proto::empty_env >::result_type child19; typedef typename evaluator::impl< A20 , Context , proto::empty_env >::result_type child20; typedef typename evaluator::impl< A21 , Context , proto::empty_env >::result_type child21; typedef typename evaluator::impl< A22 , Context , proto::empty_env >::result_type child22; typedef typename evaluator::impl< A23 , Context , proto::empty_env >::result_type child23; typedef typename evaluator::impl< A24 , Context , proto::empty_env >::result_type child24; typedef typename evaluator::impl< A25 , Context , proto::empty_env >::result_type child25; typedef typename evaluator::impl< A26 , Context , proto::empty_env >::result_type child26;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15 , child16 , child17 , child18 , child19 , child20 , child21 , child22 , child23 , child24 , child25 , child26
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15; typedef typename evaluator::impl< A16 , Context , proto::empty_env >::result_type child16; typedef typename evaluator::impl< A17 , Context , proto::empty_env >::result_type child17; typedef typename evaluator::impl< A18 , Context , proto::empty_env >::result_type child18; typedef typename evaluator::impl< A19 , Context , proto::empty_env >::result_type child19; typedef typename evaluator::impl< A20 , Context , proto::empty_env >::result_type child20; typedef typename evaluator::impl< A21 , Context , proto::empty_env >::result_type child21; typedef typename evaluator::impl< A22 , Context , proto::empty_env >::result_type child22; typedef typename evaluator::impl< A23 , Context , proto::empty_env >::result_type child23; typedef typename evaluator::impl< A24 , Context , proto::empty_env >::result_type child24; typedef typename evaluator::impl< A25 , Context , proto::empty_env >::result_type child25; typedef typename evaluator::impl< A26 , Context , proto::empty_env >::result_type child26; typedef typename evaluator::impl< A27 , Context , proto::empty_env >::result_type child27;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15 , child16 , child17 , child18 , child19 , child20 , child21 , child22 , child23 , child24 , child25 , child26 , child27
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15; typedef typename evaluator::impl< A16 , Context , proto::empty_env >::result_type child16; typedef typename evaluator::impl< A17 , Context , proto::empty_env >::result_type child17; typedef typename evaluator::impl< A18 , Context , proto::empty_env >::result_type child18; typedef typename evaluator::impl< A19 , Context , proto::empty_env >::result_type child19; typedef typename evaluator::impl< A20 , Context , proto::empty_env >::result_type child20; typedef typename evaluator::impl< A21 , Context , proto::empty_env >::result_type child21; typedef typename evaluator::impl< A22 , Context , proto::empty_env >::result_type child22; typedef typename evaluator::impl< A23 , Context , proto::empty_env >::result_type child23; typedef typename evaluator::impl< A24 , Context , proto::empty_env >::result_type child24; typedef typename evaluator::impl< A25 , Context , proto::empty_env >::result_type child25; typedef typename evaluator::impl< A26 , Context , proto::empty_env >::result_type child26; typedef typename evaluator::impl< A27 , Context , proto::empty_env >::result_type child27; typedef typename evaluator::impl< A28 , Context , proto::empty_env >::result_type child28;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15 , child16 , child17 , child18 , child19 , child20 , child21 , child22 , child23 , child24 , child25 , child26 , child27 , child28
                    )
                >::type
                type;
        };
    
    
    
    
    
    
    
        template <typename Context, typename A0 , typename A1 , typename A2 , typename A3 , typename A4 , typename A5 , typename A6 , typename A7 , typename A8 , typename A9 , typename A10 , typename A11 , typename A12 , typename A13 , typename A14 , typename A15 , typename A16 , typename A17 , typename A18 , typename A19 , typename A20 , typename A21 , typename A22 , typename A23 , typename A24 , typename A25 , typename A26 , typename A27 , typename A28 , typename A29>
        struct mem_fun_ptr_eval<Context, A0 , A1 , A2 , A3 , A4 , A5 , A6 , A7 , A8 , A9 , A10 , A11 , A12 , A13 , A14 , A15 , A16 , A17 , A18 , A19 , A20 , A21 , A22 , A23 , A24 , A25 , A26 , A27 , A28 , A29>
        {
            typedef typename evaluator::impl< A0 , Context , proto::empty_env >::result_type child0; typedef typename evaluator::impl< A1 , Context , proto::empty_env >::result_type child1; typedef typename evaluator::impl< A2 , Context , proto::empty_env >::result_type child2; typedef typename evaluator::impl< A3 , Context , proto::empty_env >::result_type child3; typedef typename evaluator::impl< A4 , Context , proto::empty_env >::result_type child4; typedef typename evaluator::impl< A5 , Context , proto::empty_env >::result_type child5; typedef typename evaluator::impl< A6 , Context , proto::empty_env >::result_type child6; typedef typename evaluator::impl< A7 , Context , proto::empty_env >::result_type child7; typedef typename evaluator::impl< A8 , Context , proto::empty_env >::result_type child8; typedef typename evaluator::impl< A9 , Context , proto::empty_env >::result_type child9; typedef typename evaluator::impl< A10 , Context , proto::empty_env >::result_type child10; typedef typename evaluator::impl< A11 , Context , proto::empty_env >::result_type child11; typedef typename evaluator::impl< A12 , Context , proto::empty_env >::result_type child12; typedef typename evaluator::impl< A13 , Context , proto::empty_env >::result_type child13; typedef typename evaluator::impl< A14 , Context , proto::empty_env >::result_type child14; typedef typename evaluator::impl< A15 , Context , proto::empty_env >::result_type child15; typedef typename evaluator::impl< A16 , Context , proto::empty_env >::result_type child16; typedef typename evaluator::impl< A17 , Context , proto::empty_env >::result_type child17; typedef typename evaluator::impl< A18 , Context , proto::empty_env >::result_type child18; typedef typename evaluator::impl< A19 , Context , proto::empty_env >::result_type child19; typedef typename evaluator::impl< A20 , Context , proto::empty_env >::result_type child20; typedef typename evaluator::impl< A21 , Context , proto::empty_env >::result_type child21; typedef typename evaluator::impl< A22 , Context , proto::empty_env >::result_type child22; typedef typename evaluator::impl< A23 , Context , proto::empty_env >::result_type child23; typedef typename evaluator::impl< A24 , Context , proto::empty_env >::result_type child24; typedef typename evaluator::impl< A25 , Context , proto::empty_env >::result_type child25; typedef typename evaluator::impl< A26 , Context , proto::empty_env >::result_type child26; typedef typename evaluator::impl< A27 , Context , proto::empty_env >::result_type child27; typedef typename evaluator::impl< A28 , Context , proto::empty_env >::result_type child28; typedef typename evaluator::impl< A29 , Context , proto::empty_env >::result_type child29;
            typedef
                typename boost::result_of<
                    child1(
                        child1 , child2 , child3 , child4 , child5 , child6 , child7 , child8 , child9 , child10 , child11 , child12 , child13 , child14 , child15 , child16 , child17 , child18 , child19 , child20 , child21 , child22 , child23 , child24 , child25 , child26 , child27 , child28 , child29
                    )
                >::type
                type;
        };
