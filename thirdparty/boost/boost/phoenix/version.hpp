/*=============================================================================
    Copyright (c) 2005-2008 <PERSON><PERSON><PERSON>
    Copyright (c) 2005-2010 <PERSON>
    Copyright (c) 2014-2015 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#ifndef BOOST_PHOENIX_VERSION_HPP
#define BOOST_PHOENIX_VERSION_HPP

///////////////////////////////////////////////////////////////////////////////
//
//  This is the version of the library
//
///////////////////////////////////////////////////////////////////////////////
#define BOOST_PHOENIX_VERSION   0x3200    // 3.2.0

#include <boost/predef/version_number.h>
#define BOOST_PHOENIX_VERSION_NUMBER = BOOST_VERSION_NUMBER(3,2,0)

#endif
