/*==============================================================================
    Copyright (c) 2010 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/

#undef BOOST_PHOENIX_typename_A
#undef BOOST_PHOENIX_typename_A_void
#undef BOOST_PHOENIX_A
#undef BOOST_PHOENIX_A_a
#undef BOOST_PHOENIX_A_ref
#undef BOOST_PHOENIX_A_const_ref
#undef BOOST_PHOENIX_A_ref_a
#undef BOOST_PHOENIX_A_const_ref_a
#undef BOOST_PHOENIX_a

#if BOOST_PHOENIX_IS_ITERATING

#undef PHEONIX_ITERATION
#undef BOOST_PHOENIX_PERM_A
#undef BOOST_PHOENIX_PERM_A_a

#undef BOOST_PHOENIX_M0_R
#undef BOOST_PHOENIX_M0
#undef BOOST_PHOENIX_M1_R_R
#undef BOOST_PHOENIX_M1_R
#undef BOOST_PHOENIX_M1
#undef BOOST_PHOENIX_M2
#undef BOOST_PHOENIX_M3
#undef BOOST_PHOENIX_PERM_SEQ
#undef BOOST_PHOENIX_PERM_SIZE
#undef BOOST_PHOENIX_PERM_ELEM

#endif
