/*
Copyright <PERSON> 2013-2015
Copyright (c) Microsoft Corporation 2014
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
http://www.boost.org/LICENSE_1_0.txt)
*/

#if !defined(BOOST_PREDEF_PLATFORM_H) || defined(BOOST_PREDEF_INTERNAL_GENERATE_TESTS)
#ifndef BOOST_PREDEF_PLATFORM_H
#define BOOST_PREDEF_PLATFORM_H
#endif

#include <boost/predef/platform/android.h>
#include <boost/predef/platform/cloudabi.h>
#include <boost/predef/platform/mingw.h>
#include <boost/predef/platform/mingw32.h>
#include <boost/predef/platform/mingw64.h>
#include <boost/predef/platform/windows_uwp.h>
#include <boost/predef/platform/windows_desktop.h>
#include <boost/predef/platform/windows_phone.h>
#include <boost/predef/platform/windows_server.h>
#include <boost/predef/platform/windows_store.h>
#include <boost/predef/platform/windows_system.h>
#include <boost/predef/platform/windows_runtime.h> // deprecated
#include <boost/predef/platform/ios.h>

#endif
