/*
Copyright Rene Rivera 2008-2015
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
http://www.boost.org/LICENSE_1_0.txt)
*/

#if !defined(BOOST_PREDEF_ARCHITECTURE_H) || defined(BOOST_PREDEF_INTERNAL_GENERATE_TESTS)
#ifndef BOOST_PREDEF_ARCHITECTURE_H
#define BOOST_PREDEF_ARCHITECTURE_H
#endif

#include <boost/predef/architecture/alpha.h>
#include <boost/predef/architecture/arm.h>
#include <boost/predef/architecture/blackfin.h>
#include <boost/predef/architecture/convex.h>
#include <boost/predef/architecture/e2k.h>
#include <boost/predef/architecture/ia64.h>
#include <boost/predef/architecture/m68k.h>
#include <boost/predef/architecture/mips.h>
#include <boost/predef/architecture/parisc.h>
#include <boost/predef/architecture/ppc.h>
#include <boost/predef/architecture/ptx.h>
#include <boost/predef/architecture/pyramid.h>
#include <boost/predef/architecture/riscv.h>
#include <boost/predef/architecture/rs6k.h>
#include <boost/predef/architecture/sparc.h>
#include <boost/predef/architecture/superh.h>
#include <boost/predef/architecture/sys370.h>
#include <boost/predef/architecture/sys390.h>
#include <boost/predef/architecture/x86.h>
#include <boost/predef/architecture/z.h>

#endif
