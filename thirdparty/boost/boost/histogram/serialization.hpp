// Copyright 2015-2019 <PERSON>
//
// Distributed under the Boost Software License, Version 1.0.
// (See accompanying file LICENSE_1_0.txt
// or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_HISTOGRAM_SERIALIZATION_HPP
#define BOOST_HISTOGRAM_SERIALIZATION_HPP

#include <boost/serialization/array.hpp>
#include <boost/serialization/map.hpp>
#include <boost/serialization/string.hpp>
#include <boost/serialization/vector.hpp>

/**
  \file boost/histogram/serialization.hpp

  Headers from
  [Boost.Serialization](https://www.boost.org/doc/libs/develop/libs/serialization/doc/index.html)
  needed to serialize STL types that are used internally by the Boost.Histogram classes.
 */

#endif
