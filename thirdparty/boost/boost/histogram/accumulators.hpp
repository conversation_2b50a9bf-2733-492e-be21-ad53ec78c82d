// Copyright 2019 <PERSON>
//
// Distributed under the Boost Software License, Version 1.0.
// (See accompanying file LICENSE_1_0.txt
// or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_HISTOGRAM_ACCUMULATORS_HPP
#define BOOST_HISTOGRAM_ACCUMULATORS_HPP

/**
  \file boost/histogram/accumulators.hpp
  Includes all accumulator headers of the Boost.Histogram library.

  Extra header not automatically included:
    - [boost/histogram/accumulators/ostream.hpp][1]

  [1]: histogram/reference.html#header.boost.histogram.accumulators.ostream_hpp
*/

#include <boost/histogram/accumulators/count.hpp>
#include <boost/histogram/accumulators/mean.hpp>
#include <boost/histogram/accumulators/sum.hpp>
#include <boost/histogram/accumulators/thread_safe.hpp>
#include <boost/histogram/accumulators/weighted_mean.hpp>
#include <boost/histogram/accumulators/weighted_sum.hpp>

#endif
