//  (C) Copyright <PERSON> 2004
//  Distributed under the Boost Software License, Version 1.0. (See
//  accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_DETAIL_PROPERTY_HPP
#define BOOST_DETAIL_PROPERTY_HPP

#include <utility> // for std::pair
#include <boost/type_traits/same_traits.hpp> // for is_same

namespace boost
{

namespace detail
{

    struct error_property_not_found
    {
    };

} // namespace detail
} // namespace boost

#endif // BOOST_DETAIL_PROPERTY_HPP
