// Copyright (c) 2016 <PERSON><PERSON>ens D<PERSON>rgenstern
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_PROCESS_DETAIL_WINDOWS_HANDLER_HPP_
#define BOOST_PROCESS_DETAIL_WINDOWS_HANDLER_HPP_

#include <boost/process/detail/handler_base.hpp>

namespace boost { namespace process { namespace detail { namespace windows {

//does not extend anything.
struct handler_base_ext : handler_base {};

}}}}



#endif /* BOOST_PROCESS_DETAIL_WINDOWS_HANDLER_HPP_ */
