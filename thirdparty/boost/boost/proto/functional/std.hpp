///////////////////////////////////////////////////////////////////////////////
/// \file std.hpp
/// Proto callables for things found in the std library
//
//  Copyright 2010 <PERSON>. Distributed under the Boost
//  Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_PROTO_FUNCTIONAL_STD_HPP_EAN_11_27_2010
#define BOOST_PROTO_FUNCTIONAL_STD_HPP_EAN_11_27_2010

#include <boost/proto/functional/std/utility.hpp>
#include <boost/proto/functional/std/iterator.hpp>

#endif
