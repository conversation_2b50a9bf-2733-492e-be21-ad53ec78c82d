# /* Copyright (C) 2001
#  * Housemarque Oy
#  * http://www.housemarque.com
#  *
#  * Distributed under the Boost Software License, Version 1.0. (See
#  * accompanying file LICENSE_1_0.txt or copy at
#  * http://www.boost.org/LICENSE_1_0.txt)
#  */
#
# /* Revised by <PERSON> (2002) */
# /* Revised by <PERSON> (2020) */
#
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_LIST_DETAIL_FOLD_RIGHT_512_HPP
# define BOOST_PREPROCESSOR_LIST_DETAIL_FOLD_RIGHT_512_HPP
#
# define BOOST_PP_LIST_FOLD_RIGHT_257(o, s, l) BOOST_PP_LIST_FOLD_LEFT_257(o, s, BOOST_PP_LIST_REVERSE_D(257, l))
# define BOOST_PP_LIST_FOLD_RIGHT_258(o, s, l) BOOST_PP_LIST_FOLD_LEFT_258(o, s, BOOST_PP_LIST_REVERSE_D(258, l))
# define BOOST_PP_LIST_FOLD_RIGHT_259(o, s, l) BOOST_PP_LIST_FOLD_LEFT_259(o, s, BOOST_PP_LIST_REVERSE_D(259, l))
# define BOOST_PP_LIST_FOLD_RIGHT_260(o, s, l) BOOST_PP_LIST_FOLD_LEFT_260(o, s, BOOST_PP_LIST_REVERSE_D(260, l))
# define BOOST_PP_LIST_FOLD_RIGHT_261(o, s, l) BOOST_PP_LIST_FOLD_LEFT_261(o, s, BOOST_PP_LIST_REVERSE_D(261, l))
# define BOOST_PP_LIST_FOLD_RIGHT_262(o, s, l) BOOST_PP_LIST_FOLD_LEFT_262(o, s, BOOST_PP_LIST_REVERSE_D(262, l))
# define BOOST_PP_LIST_FOLD_RIGHT_263(o, s, l) BOOST_PP_LIST_FOLD_LEFT_263(o, s, BOOST_PP_LIST_REVERSE_D(263, l))
# define BOOST_PP_LIST_FOLD_RIGHT_264(o, s, l) BOOST_PP_LIST_FOLD_LEFT_264(o, s, BOOST_PP_LIST_REVERSE_D(264, l))
# define BOOST_PP_LIST_FOLD_RIGHT_265(o, s, l) BOOST_PP_LIST_FOLD_LEFT_265(o, s, BOOST_PP_LIST_REVERSE_D(265, l))
# define BOOST_PP_LIST_FOLD_RIGHT_266(o, s, l) BOOST_PP_LIST_FOLD_LEFT_266(o, s, BOOST_PP_LIST_REVERSE_D(266, l))
# define BOOST_PP_LIST_FOLD_RIGHT_267(o, s, l) BOOST_PP_LIST_FOLD_LEFT_267(o, s, BOOST_PP_LIST_REVERSE_D(267, l))
# define BOOST_PP_LIST_FOLD_RIGHT_268(o, s, l) BOOST_PP_LIST_FOLD_LEFT_268(o, s, BOOST_PP_LIST_REVERSE_D(268, l))
# define BOOST_PP_LIST_FOLD_RIGHT_269(o, s, l) BOOST_PP_LIST_FOLD_LEFT_269(o, s, BOOST_PP_LIST_REVERSE_D(269, l))
# define BOOST_PP_LIST_FOLD_RIGHT_270(o, s, l) BOOST_PP_LIST_FOLD_LEFT_270(o, s, BOOST_PP_LIST_REVERSE_D(270, l))
# define BOOST_PP_LIST_FOLD_RIGHT_271(o, s, l) BOOST_PP_LIST_FOLD_LEFT_271(o, s, BOOST_PP_LIST_REVERSE_D(271, l))
# define BOOST_PP_LIST_FOLD_RIGHT_272(o, s, l) BOOST_PP_LIST_FOLD_LEFT_272(o, s, BOOST_PP_LIST_REVERSE_D(272, l))
# define BOOST_PP_LIST_FOLD_RIGHT_273(o, s, l) BOOST_PP_LIST_FOLD_LEFT_273(o, s, BOOST_PP_LIST_REVERSE_D(273, l))
# define BOOST_PP_LIST_FOLD_RIGHT_274(o, s, l) BOOST_PP_LIST_FOLD_LEFT_274(o, s, BOOST_PP_LIST_REVERSE_D(274, l))
# define BOOST_PP_LIST_FOLD_RIGHT_275(o, s, l) BOOST_PP_LIST_FOLD_LEFT_275(o, s, BOOST_PP_LIST_REVERSE_D(275, l))
# define BOOST_PP_LIST_FOLD_RIGHT_276(o, s, l) BOOST_PP_LIST_FOLD_LEFT_276(o, s, BOOST_PP_LIST_REVERSE_D(276, l))
# define BOOST_PP_LIST_FOLD_RIGHT_277(o, s, l) BOOST_PP_LIST_FOLD_LEFT_277(o, s, BOOST_PP_LIST_REVERSE_D(277, l))
# define BOOST_PP_LIST_FOLD_RIGHT_278(o, s, l) BOOST_PP_LIST_FOLD_LEFT_278(o, s, BOOST_PP_LIST_REVERSE_D(278, l))
# define BOOST_PP_LIST_FOLD_RIGHT_279(o, s, l) BOOST_PP_LIST_FOLD_LEFT_279(o, s, BOOST_PP_LIST_REVERSE_D(279, l))
# define BOOST_PP_LIST_FOLD_RIGHT_280(o, s, l) BOOST_PP_LIST_FOLD_LEFT_280(o, s, BOOST_PP_LIST_REVERSE_D(280, l))
# define BOOST_PP_LIST_FOLD_RIGHT_281(o, s, l) BOOST_PP_LIST_FOLD_LEFT_281(o, s, BOOST_PP_LIST_REVERSE_D(281, l))
# define BOOST_PP_LIST_FOLD_RIGHT_282(o, s, l) BOOST_PP_LIST_FOLD_LEFT_282(o, s, BOOST_PP_LIST_REVERSE_D(282, l))
# define BOOST_PP_LIST_FOLD_RIGHT_283(o, s, l) BOOST_PP_LIST_FOLD_LEFT_283(o, s, BOOST_PP_LIST_REVERSE_D(283, l))
# define BOOST_PP_LIST_FOLD_RIGHT_284(o, s, l) BOOST_PP_LIST_FOLD_LEFT_284(o, s, BOOST_PP_LIST_REVERSE_D(284, l))
# define BOOST_PP_LIST_FOLD_RIGHT_285(o, s, l) BOOST_PP_LIST_FOLD_LEFT_285(o, s, BOOST_PP_LIST_REVERSE_D(285, l))
# define BOOST_PP_LIST_FOLD_RIGHT_286(o, s, l) BOOST_PP_LIST_FOLD_LEFT_286(o, s, BOOST_PP_LIST_REVERSE_D(286, l))
# define BOOST_PP_LIST_FOLD_RIGHT_287(o, s, l) BOOST_PP_LIST_FOLD_LEFT_287(o, s, BOOST_PP_LIST_REVERSE_D(287, l))
# define BOOST_PP_LIST_FOLD_RIGHT_288(o, s, l) BOOST_PP_LIST_FOLD_LEFT_288(o, s, BOOST_PP_LIST_REVERSE_D(288, l))
# define BOOST_PP_LIST_FOLD_RIGHT_289(o, s, l) BOOST_PP_LIST_FOLD_LEFT_289(o, s, BOOST_PP_LIST_REVERSE_D(289, l))
# define BOOST_PP_LIST_FOLD_RIGHT_290(o, s, l) BOOST_PP_LIST_FOLD_LEFT_290(o, s, BOOST_PP_LIST_REVERSE_D(290, l))
# define BOOST_PP_LIST_FOLD_RIGHT_291(o, s, l) BOOST_PP_LIST_FOLD_LEFT_291(o, s, BOOST_PP_LIST_REVERSE_D(291, l))
# define BOOST_PP_LIST_FOLD_RIGHT_292(o, s, l) BOOST_PP_LIST_FOLD_LEFT_292(o, s, BOOST_PP_LIST_REVERSE_D(292, l))
# define BOOST_PP_LIST_FOLD_RIGHT_293(o, s, l) BOOST_PP_LIST_FOLD_LEFT_293(o, s, BOOST_PP_LIST_REVERSE_D(293, l))
# define BOOST_PP_LIST_FOLD_RIGHT_294(o, s, l) BOOST_PP_LIST_FOLD_LEFT_294(o, s, BOOST_PP_LIST_REVERSE_D(294, l))
# define BOOST_PP_LIST_FOLD_RIGHT_295(o, s, l) BOOST_PP_LIST_FOLD_LEFT_295(o, s, BOOST_PP_LIST_REVERSE_D(295, l))
# define BOOST_PP_LIST_FOLD_RIGHT_296(o, s, l) BOOST_PP_LIST_FOLD_LEFT_296(o, s, BOOST_PP_LIST_REVERSE_D(296, l))
# define BOOST_PP_LIST_FOLD_RIGHT_297(o, s, l) BOOST_PP_LIST_FOLD_LEFT_297(o, s, BOOST_PP_LIST_REVERSE_D(297, l))
# define BOOST_PP_LIST_FOLD_RIGHT_298(o, s, l) BOOST_PP_LIST_FOLD_LEFT_298(o, s, BOOST_PP_LIST_REVERSE_D(298, l))
# define BOOST_PP_LIST_FOLD_RIGHT_299(o, s, l) BOOST_PP_LIST_FOLD_LEFT_299(o, s, BOOST_PP_LIST_REVERSE_D(299, l))
# define BOOST_PP_LIST_FOLD_RIGHT_300(o, s, l) BOOST_PP_LIST_FOLD_LEFT_300(o, s, BOOST_PP_LIST_REVERSE_D(300, l))
# define BOOST_PP_LIST_FOLD_RIGHT_301(o, s, l) BOOST_PP_LIST_FOLD_LEFT_301(o, s, BOOST_PP_LIST_REVERSE_D(301, l))
# define BOOST_PP_LIST_FOLD_RIGHT_302(o, s, l) BOOST_PP_LIST_FOLD_LEFT_302(o, s, BOOST_PP_LIST_REVERSE_D(302, l))
# define BOOST_PP_LIST_FOLD_RIGHT_303(o, s, l) BOOST_PP_LIST_FOLD_LEFT_303(o, s, BOOST_PP_LIST_REVERSE_D(303, l))
# define BOOST_PP_LIST_FOLD_RIGHT_304(o, s, l) BOOST_PP_LIST_FOLD_LEFT_304(o, s, BOOST_PP_LIST_REVERSE_D(304, l))
# define BOOST_PP_LIST_FOLD_RIGHT_305(o, s, l) BOOST_PP_LIST_FOLD_LEFT_305(o, s, BOOST_PP_LIST_REVERSE_D(305, l))
# define BOOST_PP_LIST_FOLD_RIGHT_306(o, s, l) BOOST_PP_LIST_FOLD_LEFT_306(o, s, BOOST_PP_LIST_REVERSE_D(306, l))
# define BOOST_PP_LIST_FOLD_RIGHT_307(o, s, l) BOOST_PP_LIST_FOLD_LEFT_307(o, s, BOOST_PP_LIST_REVERSE_D(307, l))
# define BOOST_PP_LIST_FOLD_RIGHT_308(o, s, l) BOOST_PP_LIST_FOLD_LEFT_308(o, s, BOOST_PP_LIST_REVERSE_D(308, l))
# define BOOST_PP_LIST_FOLD_RIGHT_309(o, s, l) BOOST_PP_LIST_FOLD_LEFT_309(o, s, BOOST_PP_LIST_REVERSE_D(309, l))
# define BOOST_PP_LIST_FOLD_RIGHT_310(o, s, l) BOOST_PP_LIST_FOLD_LEFT_310(o, s, BOOST_PP_LIST_REVERSE_D(310, l))
# define BOOST_PP_LIST_FOLD_RIGHT_311(o, s, l) BOOST_PP_LIST_FOLD_LEFT_311(o, s, BOOST_PP_LIST_REVERSE_D(311, l))
# define BOOST_PP_LIST_FOLD_RIGHT_312(o, s, l) BOOST_PP_LIST_FOLD_LEFT_312(o, s, BOOST_PP_LIST_REVERSE_D(312, l))
# define BOOST_PP_LIST_FOLD_RIGHT_313(o, s, l) BOOST_PP_LIST_FOLD_LEFT_313(o, s, BOOST_PP_LIST_REVERSE_D(313, l))
# define BOOST_PP_LIST_FOLD_RIGHT_314(o, s, l) BOOST_PP_LIST_FOLD_LEFT_314(o, s, BOOST_PP_LIST_REVERSE_D(314, l))
# define BOOST_PP_LIST_FOLD_RIGHT_315(o, s, l) BOOST_PP_LIST_FOLD_LEFT_315(o, s, BOOST_PP_LIST_REVERSE_D(315, l))
# define BOOST_PP_LIST_FOLD_RIGHT_316(o, s, l) BOOST_PP_LIST_FOLD_LEFT_316(o, s, BOOST_PP_LIST_REVERSE_D(316, l))
# define BOOST_PP_LIST_FOLD_RIGHT_317(o, s, l) BOOST_PP_LIST_FOLD_LEFT_317(o, s, BOOST_PP_LIST_REVERSE_D(317, l))
# define BOOST_PP_LIST_FOLD_RIGHT_318(o, s, l) BOOST_PP_LIST_FOLD_LEFT_318(o, s, BOOST_PP_LIST_REVERSE_D(318, l))
# define BOOST_PP_LIST_FOLD_RIGHT_319(o, s, l) BOOST_PP_LIST_FOLD_LEFT_319(o, s, BOOST_PP_LIST_REVERSE_D(319, l))
# define BOOST_PP_LIST_FOLD_RIGHT_320(o, s, l) BOOST_PP_LIST_FOLD_LEFT_320(o, s, BOOST_PP_LIST_REVERSE_D(320, l))
# define BOOST_PP_LIST_FOLD_RIGHT_321(o, s, l) BOOST_PP_LIST_FOLD_LEFT_321(o, s, BOOST_PP_LIST_REVERSE_D(321, l))
# define BOOST_PP_LIST_FOLD_RIGHT_322(o, s, l) BOOST_PP_LIST_FOLD_LEFT_322(o, s, BOOST_PP_LIST_REVERSE_D(322, l))
# define BOOST_PP_LIST_FOLD_RIGHT_323(o, s, l) BOOST_PP_LIST_FOLD_LEFT_323(o, s, BOOST_PP_LIST_REVERSE_D(323, l))
# define BOOST_PP_LIST_FOLD_RIGHT_324(o, s, l) BOOST_PP_LIST_FOLD_LEFT_324(o, s, BOOST_PP_LIST_REVERSE_D(324, l))
# define BOOST_PP_LIST_FOLD_RIGHT_325(o, s, l) BOOST_PP_LIST_FOLD_LEFT_325(o, s, BOOST_PP_LIST_REVERSE_D(325, l))
# define BOOST_PP_LIST_FOLD_RIGHT_326(o, s, l) BOOST_PP_LIST_FOLD_LEFT_326(o, s, BOOST_PP_LIST_REVERSE_D(326, l))
# define BOOST_PP_LIST_FOLD_RIGHT_327(o, s, l) BOOST_PP_LIST_FOLD_LEFT_327(o, s, BOOST_PP_LIST_REVERSE_D(327, l))
# define BOOST_PP_LIST_FOLD_RIGHT_328(o, s, l) BOOST_PP_LIST_FOLD_LEFT_328(o, s, BOOST_PP_LIST_REVERSE_D(328, l))
# define BOOST_PP_LIST_FOLD_RIGHT_329(o, s, l) BOOST_PP_LIST_FOLD_LEFT_329(o, s, BOOST_PP_LIST_REVERSE_D(329, l))
# define BOOST_PP_LIST_FOLD_RIGHT_330(o, s, l) BOOST_PP_LIST_FOLD_LEFT_330(o, s, BOOST_PP_LIST_REVERSE_D(330, l))
# define BOOST_PP_LIST_FOLD_RIGHT_331(o, s, l) BOOST_PP_LIST_FOLD_LEFT_331(o, s, BOOST_PP_LIST_REVERSE_D(331, l))
# define BOOST_PP_LIST_FOLD_RIGHT_332(o, s, l) BOOST_PP_LIST_FOLD_LEFT_332(o, s, BOOST_PP_LIST_REVERSE_D(332, l))
# define BOOST_PP_LIST_FOLD_RIGHT_333(o, s, l) BOOST_PP_LIST_FOLD_LEFT_333(o, s, BOOST_PP_LIST_REVERSE_D(333, l))
# define BOOST_PP_LIST_FOLD_RIGHT_334(o, s, l) BOOST_PP_LIST_FOLD_LEFT_334(o, s, BOOST_PP_LIST_REVERSE_D(334, l))
# define BOOST_PP_LIST_FOLD_RIGHT_335(o, s, l) BOOST_PP_LIST_FOLD_LEFT_335(o, s, BOOST_PP_LIST_REVERSE_D(335, l))
# define BOOST_PP_LIST_FOLD_RIGHT_336(o, s, l) BOOST_PP_LIST_FOLD_LEFT_336(o, s, BOOST_PP_LIST_REVERSE_D(336, l))
# define BOOST_PP_LIST_FOLD_RIGHT_337(o, s, l) BOOST_PP_LIST_FOLD_LEFT_337(o, s, BOOST_PP_LIST_REVERSE_D(337, l))
# define BOOST_PP_LIST_FOLD_RIGHT_338(o, s, l) BOOST_PP_LIST_FOLD_LEFT_338(o, s, BOOST_PP_LIST_REVERSE_D(338, l))
# define BOOST_PP_LIST_FOLD_RIGHT_339(o, s, l) BOOST_PP_LIST_FOLD_LEFT_339(o, s, BOOST_PP_LIST_REVERSE_D(339, l))
# define BOOST_PP_LIST_FOLD_RIGHT_340(o, s, l) BOOST_PP_LIST_FOLD_LEFT_340(o, s, BOOST_PP_LIST_REVERSE_D(340, l))
# define BOOST_PP_LIST_FOLD_RIGHT_341(o, s, l) BOOST_PP_LIST_FOLD_LEFT_341(o, s, BOOST_PP_LIST_REVERSE_D(341, l))
# define BOOST_PP_LIST_FOLD_RIGHT_342(o, s, l) BOOST_PP_LIST_FOLD_LEFT_342(o, s, BOOST_PP_LIST_REVERSE_D(342, l))
# define BOOST_PP_LIST_FOLD_RIGHT_343(o, s, l) BOOST_PP_LIST_FOLD_LEFT_343(o, s, BOOST_PP_LIST_REVERSE_D(343, l))
# define BOOST_PP_LIST_FOLD_RIGHT_344(o, s, l) BOOST_PP_LIST_FOLD_LEFT_344(o, s, BOOST_PP_LIST_REVERSE_D(344, l))
# define BOOST_PP_LIST_FOLD_RIGHT_345(o, s, l) BOOST_PP_LIST_FOLD_LEFT_345(o, s, BOOST_PP_LIST_REVERSE_D(345, l))
# define BOOST_PP_LIST_FOLD_RIGHT_346(o, s, l) BOOST_PP_LIST_FOLD_LEFT_346(o, s, BOOST_PP_LIST_REVERSE_D(346, l))
# define BOOST_PP_LIST_FOLD_RIGHT_347(o, s, l) BOOST_PP_LIST_FOLD_LEFT_347(o, s, BOOST_PP_LIST_REVERSE_D(347, l))
# define BOOST_PP_LIST_FOLD_RIGHT_348(o, s, l) BOOST_PP_LIST_FOLD_LEFT_348(o, s, BOOST_PP_LIST_REVERSE_D(348, l))
# define BOOST_PP_LIST_FOLD_RIGHT_349(o, s, l) BOOST_PP_LIST_FOLD_LEFT_349(o, s, BOOST_PP_LIST_REVERSE_D(349, l))
# define BOOST_PP_LIST_FOLD_RIGHT_350(o, s, l) BOOST_PP_LIST_FOLD_LEFT_350(o, s, BOOST_PP_LIST_REVERSE_D(350, l))
# define BOOST_PP_LIST_FOLD_RIGHT_351(o, s, l) BOOST_PP_LIST_FOLD_LEFT_351(o, s, BOOST_PP_LIST_REVERSE_D(351, l))
# define BOOST_PP_LIST_FOLD_RIGHT_352(o, s, l) BOOST_PP_LIST_FOLD_LEFT_352(o, s, BOOST_PP_LIST_REVERSE_D(352, l))
# define BOOST_PP_LIST_FOLD_RIGHT_353(o, s, l) BOOST_PP_LIST_FOLD_LEFT_353(o, s, BOOST_PP_LIST_REVERSE_D(353, l))
# define BOOST_PP_LIST_FOLD_RIGHT_354(o, s, l) BOOST_PP_LIST_FOLD_LEFT_354(o, s, BOOST_PP_LIST_REVERSE_D(354, l))
# define BOOST_PP_LIST_FOLD_RIGHT_355(o, s, l) BOOST_PP_LIST_FOLD_LEFT_355(o, s, BOOST_PP_LIST_REVERSE_D(355, l))
# define BOOST_PP_LIST_FOLD_RIGHT_356(o, s, l) BOOST_PP_LIST_FOLD_LEFT_356(o, s, BOOST_PP_LIST_REVERSE_D(356, l))
# define BOOST_PP_LIST_FOLD_RIGHT_357(o, s, l) BOOST_PP_LIST_FOLD_LEFT_357(o, s, BOOST_PP_LIST_REVERSE_D(357, l))
# define BOOST_PP_LIST_FOLD_RIGHT_358(o, s, l) BOOST_PP_LIST_FOLD_LEFT_358(o, s, BOOST_PP_LIST_REVERSE_D(358, l))
# define BOOST_PP_LIST_FOLD_RIGHT_359(o, s, l) BOOST_PP_LIST_FOLD_LEFT_359(o, s, BOOST_PP_LIST_REVERSE_D(359, l))
# define BOOST_PP_LIST_FOLD_RIGHT_360(o, s, l) BOOST_PP_LIST_FOLD_LEFT_360(o, s, BOOST_PP_LIST_REVERSE_D(360, l))
# define BOOST_PP_LIST_FOLD_RIGHT_361(o, s, l) BOOST_PP_LIST_FOLD_LEFT_361(o, s, BOOST_PP_LIST_REVERSE_D(361, l))
# define BOOST_PP_LIST_FOLD_RIGHT_362(o, s, l) BOOST_PP_LIST_FOLD_LEFT_362(o, s, BOOST_PP_LIST_REVERSE_D(362, l))
# define BOOST_PP_LIST_FOLD_RIGHT_363(o, s, l) BOOST_PP_LIST_FOLD_LEFT_363(o, s, BOOST_PP_LIST_REVERSE_D(363, l))
# define BOOST_PP_LIST_FOLD_RIGHT_364(o, s, l) BOOST_PP_LIST_FOLD_LEFT_364(o, s, BOOST_PP_LIST_REVERSE_D(364, l))
# define BOOST_PP_LIST_FOLD_RIGHT_365(o, s, l) BOOST_PP_LIST_FOLD_LEFT_365(o, s, BOOST_PP_LIST_REVERSE_D(365, l))
# define BOOST_PP_LIST_FOLD_RIGHT_366(o, s, l) BOOST_PP_LIST_FOLD_LEFT_366(o, s, BOOST_PP_LIST_REVERSE_D(366, l))
# define BOOST_PP_LIST_FOLD_RIGHT_367(o, s, l) BOOST_PP_LIST_FOLD_LEFT_367(o, s, BOOST_PP_LIST_REVERSE_D(367, l))
# define BOOST_PP_LIST_FOLD_RIGHT_368(o, s, l) BOOST_PP_LIST_FOLD_LEFT_368(o, s, BOOST_PP_LIST_REVERSE_D(368, l))
# define BOOST_PP_LIST_FOLD_RIGHT_369(o, s, l) BOOST_PP_LIST_FOLD_LEFT_369(o, s, BOOST_PP_LIST_REVERSE_D(369, l))
# define BOOST_PP_LIST_FOLD_RIGHT_370(o, s, l) BOOST_PP_LIST_FOLD_LEFT_370(o, s, BOOST_PP_LIST_REVERSE_D(370, l))
# define BOOST_PP_LIST_FOLD_RIGHT_371(o, s, l) BOOST_PP_LIST_FOLD_LEFT_371(o, s, BOOST_PP_LIST_REVERSE_D(371, l))
# define BOOST_PP_LIST_FOLD_RIGHT_372(o, s, l) BOOST_PP_LIST_FOLD_LEFT_372(o, s, BOOST_PP_LIST_REVERSE_D(372, l))
# define BOOST_PP_LIST_FOLD_RIGHT_373(o, s, l) BOOST_PP_LIST_FOLD_LEFT_373(o, s, BOOST_PP_LIST_REVERSE_D(373, l))
# define BOOST_PP_LIST_FOLD_RIGHT_374(o, s, l) BOOST_PP_LIST_FOLD_LEFT_374(o, s, BOOST_PP_LIST_REVERSE_D(374, l))
# define BOOST_PP_LIST_FOLD_RIGHT_375(o, s, l) BOOST_PP_LIST_FOLD_LEFT_375(o, s, BOOST_PP_LIST_REVERSE_D(375, l))
# define BOOST_PP_LIST_FOLD_RIGHT_376(o, s, l) BOOST_PP_LIST_FOLD_LEFT_376(o, s, BOOST_PP_LIST_REVERSE_D(376, l))
# define BOOST_PP_LIST_FOLD_RIGHT_377(o, s, l) BOOST_PP_LIST_FOLD_LEFT_377(o, s, BOOST_PP_LIST_REVERSE_D(377, l))
# define BOOST_PP_LIST_FOLD_RIGHT_378(o, s, l) BOOST_PP_LIST_FOLD_LEFT_378(o, s, BOOST_PP_LIST_REVERSE_D(378, l))
# define BOOST_PP_LIST_FOLD_RIGHT_379(o, s, l) BOOST_PP_LIST_FOLD_LEFT_379(o, s, BOOST_PP_LIST_REVERSE_D(379, l))
# define BOOST_PP_LIST_FOLD_RIGHT_380(o, s, l) BOOST_PP_LIST_FOLD_LEFT_380(o, s, BOOST_PP_LIST_REVERSE_D(380, l))
# define BOOST_PP_LIST_FOLD_RIGHT_381(o, s, l) BOOST_PP_LIST_FOLD_LEFT_381(o, s, BOOST_PP_LIST_REVERSE_D(381, l))
# define BOOST_PP_LIST_FOLD_RIGHT_382(o, s, l) BOOST_PP_LIST_FOLD_LEFT_382(o, s, BOOST_PP_LIST_REVERSE_D(382, l))
# define BOOST_PP_LIST_FOLD_RIGHT_383(o, s, l) BOOST_PP_LIST_FOLD_LEFT_383(o, s, BOOST_PP_LIST_REVERSE_D(383, l))
# define BOOST_PP_LIST_FOLD_RIGHT_384(o, s, l) BOOST_PP_LIST_FOLD_LEFT_384(o, s, BOOST_PP_LIST_REVERSE_D(384, l))
# define BOOST_PP_LIST_FOLD_RIGHT_385(o, s, l) BOOST_PP_LIST_FOLD_LEFT_385(o, s, BOOST_PP_LIST_REVERSE_D(385, l))
# define BOOST_PP_LIST_FOLD_RIGHT_386(o, s, l) BOOST_PP_LIST_FOLD_LEFT_386(o, s, BOOST_PP_LIST_REVERSE_D(386, l))
# define BOOST_PP_LIST_FOLD_RIGHT_387(o, s, l) BOOST_PP_LIST_FOLD_LEFT_387(o, s, BOOST_PP_LIST_REVERSE_D(387, l))
# define BOOST_PP_LIST_FOLD_RIGHT_388(o, s, l) BOOST_PP_LIST_FOLD_LEFT_388(o, s, BOOST_PP_LIST_REVERSE_D(388, l))
# define BOOST_PP_LIST_FOLD_RIGHT_389(o, s, l) BOOST_PP_LIST_FOLD_LEFT_389(o, s, BOOST_PP_LIST_REVERSE_D(389, l))
# define BOOST_PP_LIST_FOLD_RIGHT_390(o, s, l) BOOST_PP_LIST_FOLD_LEFT_390(o, s, BOOST_PP_LIST_REVERSE_D(390, l))
# define BOOST_PP_LIST_FOLD_RIGHT_391(o, s, l) BOOST_PP_LIST_FOLD_LEFT_391(o, s, BOOST_PP_LIST_REVERSE_D(391, l))
# define BOOST_PP_LIST_FOLD_RIGHT_392(o, s, l) BOOST_PP_LIST_FOLD_LEFT_392(o, s, BOOST_PP_LIST_REVERSE_D(392, l))
# define BOOST_PP_LIST_FOLD_RIGHT_393(o, s, l) BOOST_PP_LIST_FOLD_LEFT_393(o, s, BOOST_PP_LIST_REVERSE_D(393, l))
# define BOOST_PP_LIST_FOLD_RIGHT_394(o, s, l) BOOST_PP_LIST_FOLD_LEFT_394(o, s, BOOST_PP_LIST_REVERSE_D(394, l))
# define BOOST_PP_LIST_FOLD_RIGHT_395(o, s, l) BOOST_PP_LIST_FOLD_LEFT_395(o, s, BOOST_PP_LIST_REVERSE_D(395, l))
# define BOOST_PP_LIST_FOLD_RIGHT_396(o, s, l) BOOST_PP_LIST_FOLD_LEFT_396(o, s, BOOST_PP_LIST_REVERSE_D(396, l))
# define BOOST_PP_LIST_FOLD_RIGHT_397(o, s, l) BOOST_PP_LIST_FOLD_LEFT_397(o, s, BOOST_PP_LIST_REVERSE_D(397, l))
# define BOOST_PP_LIST_FOLD_RIGHT_398(o, s, l) BOOST_PP_LIST_FOLD_LEFT_398(o, s, BOOST_PP_LIST_REVERSE_D(398, l))
# define BOOST_PP_LIST_FOLD_RIGHT_399(o, s, l) BOOST_PP_LIST_FOLD_LEFT_399(o, s, BOOST_PP_LIST_REVERSE_D(399, l))
# define BOOST_PP_LIST_FOLD_RIGHT_400(o, s, l) BOOST_PP_LIST_FOLD_LEFT_400(o, s, BOOST_PP_LIST_REVERSE_D(400, l))
# define BOOST_PP_LIST_FOLD_RIGHT_401(o, s, l) BOOST_PP_LIST_FOLD_LEFT_401(o, s, BOOST_PP_LIST_REVERSE_D(401, l))
# define BOOST_PP_LIST_FOLD_RIGHT_402(o, s, l) BOOST_PP_LIST_FOLD_LEFT_402(o, s, BOOST_PP_LIST_REVERSE_D(402, l))
# define BOOST_PP_LIST_FOLD_RIGHT_403(o, s, l) BOOST_PP_LIST_FOLD_LEFT_403(o, s, BOOST_PP_LIST_REVERSE_D(403, l))
# define BOOST_PP_LIST_FOLD_RIGHT_404(o, s, l) BOOST_PP_LIST_FOLD_LEFT_404(o, s, BOOST_PP_LIST_REVERSE_D(404, l))
# define BOOST_PP_LIST_FOLD_RIGHT_405(o, s, l) BOOST_PP_LIST_FOLD_LEFT_405(o, s, BOOST_PP_LIST_REVERSE_D(405, l))
# define BOOST_PP_LIST_FOLD_RIGHT_406(o, s, l) BOOST_PP_LIST_FOLD_LEFT_406(o, s, BOOST_PP_LIST_REVERSE_D(406, l))
# define BOOST_PP_LIST_FOLD_RIGHT_407(o, s, l) BOOST_PP_LIST_FOLD_LEFT_407(o, s, BOOST_PP_LIST_REVERSE_D(407, l))
# define BOOST_PP_LIST_FOLD_RIGHT_408(o, s, l) BOOST_PP_LIST_FOLD_LEFT_408(o, s, BOOST_PP_LIST_REVERSE_D(408, l))
# define BOOST_PP_LIST_FOLD_RIGHT_409(o, s, l) BOOST_PP_LIST_FOLD_LEFT_409(o, s, BOOST_PP_LIST_REVERSE_D(409, l))
# define BOOST_PP_LIST_FOLD_RIGHT_410(o, s, l) BOOST_PP_LIST_FOLD_LEFT_410(o, s, BOOST_PP_LIST_REVERSE_D(410, l))
# define BOOST_PP_LIST_FOLD_RIGHT_411(o, s, l) BOOST_PP_LIST_FOLD_LEFT_411(o, s, BOOST_PP_LIST_REVERSE_D(411, l))
# define BOOST_PP_LIST_FOLD_RIGHT_412(o, s, l) BOOST_PP_LIST_FOLD_LEFT_412(o, s, BOOST_PP_LIST_REVERSE_D(412, l))
# define BOOST_PP_LIST_FOLD_RIGHT_413(o, s, l) BOOST_PP_LIST_FOLD_LEFT_413(o, s, BOOST_PP_LIST_REVERSE_D(413, l))
# define BOOST_PP_LIST_FOLD_RIGHT_414(o, s, l) BOOST_PP_LIST_FOLD_LEFT_414(o, s, BOOST_PP_LIST_REVERSE_D(414, l))
# define BOOST_PP_LIST_FOLD_RIGHT_415(o, s, l) BOOST_PP_LIST_FOLD_LEFT_415(o, s, BOOST_PP_LIST_REVERSE_D(415, l))
# define BOOST_PP_LIST_FOLD_RIGHT_416(o, s, l) BOOST_PP_LIST_FOLD_LEFT_416(o, s, BOOST_PP_LIST_REVERSE_D(416, l))
# define BOOST_PP_LIST_FOLD_RIGHT_417(o, s, l) BOOST_PP_LIST_FOLD_LEFT_417(o, s, BOOST_PP_LIST_REVERSE_D(417, l))
# define BOOST_PP_LIST_FOLD_RIGHT_418(o, s, l) BOOST_PP_LIST_FOLD_LEFT_418(o, s, BOOST_PP_LIST_REVERSE_D(418, l))
# define BOOST_PP_LIST_FOLD_RIGHT_419(o, s, l) BOOST_PP_LIST_FOLD_LEFT_419(o, s, BOOST_PP_LIST_REVERSE_D(419, l))
# define BOOST_PP_LIST_FOLD_RIGHT_420(o, s, l) BOOST_PP_LIST_FOLD_LEFT_420(o, s, BOOST_PP_LIST_REVERSE_D(420, l))
# define BOOST_PP_LIST_FOLD_RIGHT_421(o, s, l) BOOST_PP_LIST_FOLD_LEFT_421(o, s, BOOST_PP_LIST_REVERSE_D(421, l))
# define BOOST_PP_LIST_FOLD_RIGHT_422(o, s, l) BOOST_PP_LIST_FOLD_LEFT_422(o, s, BOOST_PP_LIST_REVERSE_D(422, l))
# define BOOST_PP_LIST_FOLD_RIGHT_423(o, s, l) BOOST_PP_LIST_FOLD_LEFT_423(o, s, BOOST_PP_LIST_REVERSE_D(423, l))
# define BOOST_PP_LIST_FOLD_RIGHT_424(o, s, l) BOOST_PP_LIST_FOLD_LEFT_424(o, s, BOOST_PP_LIST_REVERSE_D(424, l))
# define BOOST_PP_LIST_FOLD_RIGHT_425(o, s, l) BOOST_PP_LIST_FOLD_LEFT_425(o, s, BOOST_PP_LIST_REVERSE_D(425, l))
# define BOOST_PP_LIST_FOLD_RIGHT_426(o, s, l) BOOST_PP_LIST_FOLD_LEFT_426(o, s, BOOST_PP_LIST_REVERSE_D(426, l))
# define BOOST_PP_LIST_FOLD_RIGHT_427(o, s, l) BOOST_PP_LIST_FOLD_LEFT_427(o, s, BOOST_PP_LIST_REVERSE_D(427, l))
# define BOOST_PP_LIST_FOLD_RIGHT_428(o, s, l) BOOST_PP_LIST_FOLD_LEFT_428(o, s, BOOST_PP_LIST_REVERSE_D(428, l))
# define BOOST_PP_LIST_FOLD_RIGHT_429(o, s, l) BOOST_PP_LIST_FOLD_LEFT_429(o, s, BOOST_PP_LIST_REVERSE_D(429, l))
# define BOOST_PP_LIST_FOLD_RIGHT_430(o, s, l) BOOST_PP_LIST_FOLD_LEFT_430(o, s, BOOST_PP_LIST_REVERSE_D(430, l))
# define BOOST_PP_LIST_FOLD_RIGHT_431(o, s, l) BOOST_PP_LIST_FOLD_LEFT_431(o, s, BOOST_PP_LIST_REVERSE_D(431, l))
# define BOOST_PP_LIST_FOLD_RIGHT_432(o, s, l) BOOST_PP_LIST_FOLD_LEFT_432(o, s, BOOST_PP_LIST_REVERSE_D(432, l))
# define BOOST_PP_LIST_FOLD_RIGHT_433(o, s, l) BOOST_PP_LIST_FOLD_LEFT_433(o, s, BOOST_PP_LIST_REVERSE_D(433, l))
# define BOOST_PP_LIST_FOLD_RIGHT_434(o, s, l) BOOST_PP_LIST_FOLD_LEFT_434(o, s, BOOST_PP_LIST_REVERSE_D(434, l))
# define BOOST_PP_LIST_FOLD_RIGHT_435(o, s, l) BOOST_PP_LIST_FOLD_LEFT_435(o, s, BOOST_PP_LIST_REVERSE_D(435, l))
# define BOOST_PP_LIST_FOLD_RIGHT_436(o, s, l) BOOST_PP_LIST_FOLD_LEFT_436(o, s, BOOST_PP_LIST_REVERSE_D(436, l))
# define BOOST_PP_LIST_FOLD_RIGHT_437(o, s, l) BOOST_PP_LIST_FOLD_LEFT_437(o, s, BOOST_PP_LIST_REVERSE_D(437, l))
# define BOOST_PP_LIST_FOLD_RIGHT_438(o, s, l) BOOST_PP_LIST_FOLD_LEFT_438(o, s, BOOST_PP_LIST_REVERSE_D(438, l))
# define BOOST_PP_LIST_FOLD_RIGHT_439(o, s, l) BOOST_PP_LIST_FOLD_LEFT_439(o, s, BOOST_PP_LIST_REVERSE_D(439, l))
# define BOOST_PP_LIST_FOLD_RIGHT_440(o, s, l) BOOST_PP_LIST_FOLD_LEFT_440(o, s, BOOST_PP_LIST_REVERSE_D(440, l))
# define BOOST_PP_LIST_FOLD_RIGHT_441(o, s, l) BOOST_PP_LIST_FOLD_LEFT_441(o, s, BOOST_PP_LIST_REVERSE_D(441, l))
# define BOOST_PP_LIST_FOLD_RIGHT_442(o, s, l) BOOST_PP_LIST_FOLD_LEFT_442(o, s, BOOST_PP_LIST_REVERSE_D(442, l))
# define BOOST_PP_LIST_FOLD_RIGHT_443(o, s, l) BOOST_PP_LIST_FOLD_LEFT_443(o, s, BOOST_PP_LIST_REVERSE_D(443, l))
# define BOOST_PP_LIST_FOLD_RIGHT_444(o, s, l) BOOST_PP_LIST_FOLD_LEFT_444(o, s, BOOST_PP_LIST_REVERSE_D(444, l))
# define BOOST_PP_LIST_FOLD_RIGHT_445(o, s, l) BOOST_PP_LIST_FOLD_LEFT_445(o, s, BOOST_PP_LIST_REVERSE_D(445, l))
# define BOOST_PP_LIST_FOLD_RIGHT_446(o, s, l) BOOST_PP_LIST_FOLD_LEFT_446(o, s, BOOST_PP_LIST_REVERSE_D(446, l))
# define BOOST_PP_LIST_FOLD_RIGHT_447(o, s, l) BOOST_PP_LIST_FOLD_LEFT_447(o, s, BOOST_PP_LIST_REVERSE_D(447, l))
# define BOOST_PP_LIST_FOLD_RIGHT_448(o, s, l) BOOST_PP_LIST_FOLD_LEFT_448(o, s, BOOST_PP_LIST_REVERSE_D(448, l))
# define BOOST_PP_LIST_FOLD_RIGHT_449(o, s, l) BOOST_PP_LIST_FOLD_LEFT_449(o, s, BOOST_PP_LIST_REVERSE_D(449, l))
# define BOOST_PP_LIST_FOLD_RIGHT_450(o, s, l) BOOST_PP_LIST_FOLD_LEFT_450(o, s, BOOST_PP_LIST_REVERSE_D(450, l))
# define BOOST_PP_LIST_FOLD_RIGHT_451(o, s, l) BOOST_PP_LIST_FOLD_LEFT_451(o, s, BOOST_PP_LIST_REVERSE_D(451, l))
# define BOOST_PP_LIST_FOLD_RIGHT_452(o, s, l) BOOST_PP_LIST_FOLD_LEFT_452(o, s, BOOST_PP_LIST_REVERSE_D(452, l))
# define BOOST_PP_LIST_FOLD_RIGHT_453(o, s, l) BOOST_PP_LIST_FOLD_LEFT_453(o, s, BOOST_PP_LIST_REVERSE_D(453, l))
# define BOOST_PP_LIST_FOLD_RIGHT_454(o, s, l) BOOST_PP_LIST_FOLD_LEFT_454(o, s, BOOST_PP_LIST_REVERSE_D(454, l))
# define BOOST_PP_LIST_FOLD_RIGHT_455(o, s, l) BOOST_PP_LIST_FOLD_LEFT_455(o, s, BOOST_PP_LIST_REVERSE_D(455, l))
# define BOOST_PP_LIST_FOLD_RIGHT_456(o, s, l) BOOST_PP_LIST_FOLD_LEFT_456(o, s, BOOST_PP_LIST_REVERSE_D(456, l))
# define BOOST_PP_LIST_FOLD_RIGHT_457(o, s, l) BOOST_PP_LIST_FOLD_LEFT_457(o, s, BOOST_PP_LIST_REVERSE_D(457, l))
# define BOOST_PP_LIST_FOLD_RIGHT_458(o, s, l) BOOST_PP_LIST_FOLD_LEFT_458(o, s, BOOST_PP_LIST_REVERSE_D(458, l))
# define BOOST_PP_LIST_FOLD_RIGHT_459(o, s, l) BOOST_PP_LIST_FOLD_LEFT_459(o, s, BOOST_PP_LIST_REVERSE_D(459, l))
# define BOOST_PP_LIST_FOLD_RIGHT_460(o, s, l) BOOST_PP_LIST_FOLD_LEFT_460(o, s, BOOST_PP_LIST_REVERSE_D(460, l))
# define BOOST_PP_LIST_FOLD_RIGHT_461(o, s, l) BOOST_PP_LIST_FOLD_LEFT_461(o, s, BOOST_PP_LIST_REVERSE_D(461, l))
# define BOOST_PP_LIST_FOLD_RIGHT_462(o, s, l) BOOST_PP_LIST_FOLD_LEFT_462(o, s, BOOST_PP_LIST_REVERSE_D(462, l))
# define BOOST_PP_LIST_FOLD_RIGHT_463(o, s, l) BOOST_PP_LIST_FOLD_LEFT_463(o, s, BOOST_PP_LIST_REVERSE_D(463, l))
# define BOOST_PP_LIST_FOLD_RIGHT_464(o, s, l) BOOST_PP_LIST_FOLD_LEFT_464(o, s, BOOST_PP_LIST_REVERSE_D(464, l))
# define BOOST_PP_LIST_FOLD_RIGHT_465(o, s, l) BOOST_PP_LIST_FOLD_LEFT_465(o, s, BOOST_PP_LIST_REVERSE_D(465, l))
# define BOOST_PP_LIST_FOLD_RIGHT_466(o, s, l) BOOST_PP_LIST_FOLD_LEFT_466(o, s, BOOST_PP_LIST_REVERSE_D(466, l))
# define BOOST_PP_LIST_FOLD_RIGHT_467(o, s, l) BOOST_PP_LIST_FOLD_LEFT_467(o, s, BOOST_PP_LIST_REVERSE_D(467, l))
# define BOOST_PP_LIST_FOLD_RIGHT_468(o, s, l) BOOST_PP_LIST_FOLD_LEFT_468(o, s, BOOST_PP_LIST_REVERSE_D(468, l))
# define BOOST_PP_LIST_FOLD_RIGHT_469(o, s, l) BOOST_PP_LIST_FOLD_LEFT_469(o, s, BOOST_PP_LIST_REVERSE_D(469, l))
# define BOOST_PP_LIST_FOLD_RIGHT_470(o, s, l) BOOST_PP_LIST_FOLD_LEFT_470(o, s, BOOST_PP_LIST_REVERSE_D(470, l))
# define BOOST_PP_LIST_FOLD_RIGHT_471(o, s, l) BOOST_PP_LIST_FOLD_LEFT_471(o, s, BOOST_PP_LIST_REVERSE_D(471, l))
# define BOOST_PP_LIST_FOLD_RIGHT_472(o, s, l) BOOST_PP_LIST_FOLD_LEFT_472(o, s, BOOST_PP_LIST_REVERSE_D(472, l))
# define BOOST_PP_LIST_FOLD_RIGHT_473(o, s, l) BOOST_PP_LIST_FOLD_LEFT_473(o, s, BOOST_PP_LIST_REVERSE_D(473, l))
# define BOOST_PP_LIST_FOLD_RIGHT_474(o, s, l) BOOST_PP_LIST_FOLD_LEFT_474(o, s, BOOST_PP_LIST_REVERSE_D(474, l))
# define BOOST_PP_LIST_FOLD_RIGHT_475(o, s, l) BOOST_PP_LIST_FOLD_LEFT_475(o, s, BOOST_PP_LIST_REVERSE_D(475, l))
# define BOOST_PP_LIST_FOLD_RIGHT_476(o, s, l) BOOST_PP_LIST_FOLD_LEFT_476(o, s, BOOST_PP_LIST_REVERSE_D(476, l))
# define BOOST_PP_LIST_FOLD_RIGHT_477(o, s, l) BOOST_PP_LIST_FOLD_LEFT_477(o, s, BOOST_PP_LIST_REVERSE_D(477, l))
# define BOOST_PP_LIST_FOLD_RIGHT_478(o, s, l) BOOST_PP_LIST_FOLD_LEFT_478(o, s, BOOST_PP_LIST_REVERSE_D(478, l))
# define BOOST_PP_LIST_FOLD_RIGHT_479(o, s, l) BOOST_PP_LIST_FOLD_LEFT_479(o, s, BOOST_PP_LIST_REVERSE_D(479, l))
# define BOOST_PP_LIST_FOLD_RIGHT_480(o, s, l) BOOST_PP_LIST_FOLD_LEFT_480(o, s, BOOST_PP_LIST_REVERSE_D(480, l))
# define BOOST_PP_LIST_FOLD_RIGHT_481(o, s, l) BOOST_PP_LIST_FOLD_LEFT_481(o, s, BOOST_PP_LIST_REVERSE_D(481, l))
# define BOOST_PP_LIST_FOLD_RIGHT_482(o, s, l) BOOST_PP_LIST_FOLD_LEFT_482(o, s, BOOST_PP_LIST_REVERSE_D(482, l))
# define BOOST_PP_LIST_FOLD_RIGHT_483(o, s, l) BOOST_PP_LIST_FOLD_LEFT_483(o, s, BOOST_PP_LIST_REVERSE_D(483, l))
# define BOOST_PP_LIST_FOLD_RIGHT_484(o, s, l) BOOST_PP_LIST_FOLD_LEFT_484(o, s, BOOST_PP_LIST_REVERSE_D(484, l))
# define BOOST_PP_LIST_FOLD_RIGHT_485(o, s, l) BOOST_PP_LIST_FOLD_LEFT_485(o, s, BOOST_PP_LIST_REVERSE_D(485, l))
# define BOOST_PP_LIST_FOLD_RIGHT_486(o, s, l) BOOST_PP_LIST_FOLD_LEFT_486(o, s, BOOST_PP_LIST_REVERSE_D(486, l))
# define BOOST_PP_LIST_FOLD_RIGHT_487(o, s, l) BOOST_PP_LIST_FOLD_LEFT_487(o, s, BOOST_PP_LIST_REVERSE_D(487, l))
# define BOOST_PP_LIST_FOLD_RIGHT_488(o, s, l) BOOST_PP_LIST_FOLD_LEFT_488(o, s, BOOST_PP_LIST_REVERSE_D(488, l))
# define BOOST_PP_LIST_FOLD_RIGHT_489(o, s, l) BOOST_PP_LIST_FOLD_LEFT_489(o, s, BOOST_PP_LIST_REVERSE_D(489, l))
# define BOOST_PP_LIST_FOLD_RIGHT_490(o, s, l) BOOST_PP_LIST_FOLD_LEFT_490(o, s, BOOST_PP_LIST_REVERSE_D(490, l))
# define BOOST_PP_LIST_FOLD_RIGHT_491(o, s, l) BOOST_PP_LIST_FOLD_LEFT_491(o, s, BOOST_PP_LIST_REVERSE_D(491, l))
# define BOOST_PP_LIST_FOLD_RIGHT_492(o, s, l) BOOST_PP_LIST_FOLD_LEFT_492(o, s, BOOST_PP_LIST_REVERSE_D(492, l))
# define BOOST_PP_LIST_FOLD_RIGHT_493(o, s, l) BOOST_PP_LIST_FOLD_LEFT_493(o, s, BOOST_PP_LIST_REVERSE_D(493, l))
# define BOOST_PP_LIST_FOLD_RIGHT_494(o, s, l) BOOST_PP_LIST_FOLD_LEFT_494(o, s, BOOST_PP_LIST_REVERSE_D(494, l))
# define BOOST_PP_LIST_FOLD_RIGHT_495(o, s, l) BOOST_PP_LIST_FOLD_LEFT_495(o, s, BOOST_PP_LIST_REVERSE_D(495, l))
# define BOOST_PP_LIST_FOLD_RIGHT_496(o, s, l) BOOST_PP_LIST_FOLD_LEFT_496(o, s, BOOST_PP_LIST_REVERSE_D(496, l))
# define BOOST_PP_LIST_FOLD_RIGHT_497(o, s, l) BOOST_PP_LIST_FOLD_LEFT_497(o, s, BOOST_PP_LIST_REVERSE_D(497, l))
# define BOOST_PP_LIST_FOLD_RIGHT_498(o, s, l) BOOST_PP_LIST_FOLD_LEFT_498(o, s, BOOST_PP_LIST_REVERSE_D(498, l))
# define BOOST_PP_LIST_FOLD_RIGHT_499(o, s, l) BOOST_PP_LIST_FOLD_LEFT_499(o, s, BOOST_PP_LIST_REVERSE_D(499, l))
# define BOOST_PP_LIST_FOLD_RIGHT_500(o, s, l) BOOST_PP_LIST_FOLD_LEFT_500(o, s, BOOST_PP_LIST_REVERSE_D(500, l))
# define BOOST_PP_LIST_FOLD_RIGHT_501(o, s, l) BOOST_PP_LIST_FOLD_LEFT_501(o, s, BOOST_PP_LIST_REVERSE_D(501, l))
# define BOOST_PP_LIST_FOLD_RIGHT_502(o, s, l) BOOST_PP_LIST_FOLD_LEFT_502(o, s, BOOST_PP_LIST_REVERSE_D(502, l))
# define BOOST_PP_LIST_FOLD_RIGHT_503(o, s, l) BOOST_PP_LIST_FOLD_LEFT_503(o, s, BOOST_PP_LIST_REVERSE_D(503, l))
# define BOOST_PP_LIST_FOLD_RIGHT_504(o, s, l) BOOST_PP_LIST_FOLD_LEFT_504(o, s, BOOST_PP_LIST_REVERSE_D(504, l))
# define BOOST_PP_LIST_FOLD_RIGHT_505(o, s, l) BOOST_PP_LIST_FOLD_LEFT_505(o, s, BOOST_PP_LIST_REVERSE_D(505, l))
# define BOOST_PP_LIST_FOLD_RIGHT_506(o, s, l) BOOST_PP_LIST_FOLD_LEFT_506(o, s, BOOST_PP_LIST_REVERSE_D(506, l))
# define BOOST_PP_LIST_FOLD_RIGHT_507(o, s, l) BOOST_PP_LIST_FOLD_LEFT_507(o, s, BOOST_PP_LIST_REVERSE_D(507, l))
# define BOOST_PP_LIST_FOLD_RIGHT_508(o, s, l) BOOST_PP_LIST_FOLD_LEFT_508(o, s, BOOST_PP_LIST_REVERSE_D(508, l))
# define BOOST_PP_LIST_FOLD_RIGHT_509(o, s, l) BOOST_PP_LIST_FOLD_LEFT_509(o, s, BOOST_PP_LIST_REVERSE_D(509, l))
# define BOOST_PP_LIST_FOLD_RIGHT_510(o, s, l) BOOST_PP_LIST_FOLD_LEFT_510(o, s, BOOST_PP_LIST_REVERSE_D(510, l))
# define BOOST_PP_LIST_FOLD_RIGHT_511(o, s, l) BOOST_PP_LIST_FOLD_LEFT_511(o, s, BOOST_PP_LIST_REVERSE_D(511, l))
# define BOOST_PP_LIST_FOLD_RIGHT_512(o, s, l) BOOST_PP_LIST_FOLD_LEFT_512(o, s, BOOST_PP_LIST_REVERSE_D(512, l))
#
# endif
