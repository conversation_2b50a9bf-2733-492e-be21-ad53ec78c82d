# /* Copyright (C) 2001
#  * Housemarque Oy
#  * http://www.housemarque.com
#  *
#  * Distributed under the Boost Software License, Version 1.0. (See
#  * accompanying file LICENSE_1_0.txt or copy at
#  * http://www.boost.org/LICENSE_1_0.txt)
#  */
#
# /* Revised by <PERSON> (2002) */
# /* Revised by <PERSON> (2020) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_CONTROL_WHILE_512_HPP
# define BOOST_PREPROCESSOR_CONTROL_WHILE_512_HPP
#
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_257(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_258(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_259(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_260(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_261(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_262(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_263(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_264(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_265(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_266(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_267(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_268(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_269(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_270(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_271(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_272(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_273(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_274(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_275(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_276(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_277(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_278(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_279(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_280(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_281(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_282(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_283(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_284(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_285(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_286(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_287(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_288(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_289(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_290(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_291(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_292(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_293(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_294(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_295(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_296(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_297(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_298(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_299(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_300(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_301(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_302(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_303(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_304(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_305(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_306(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_307(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_308(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_309(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_310(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_311(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_312(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_313(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_314(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_315(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_316(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_317(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_318(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_319(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_320(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_321(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_322(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_323(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_324(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_325(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_326(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_327(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_328(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_329(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_330(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_331(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_332(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_333(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_334(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_335(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_336(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_337(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_338(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_339(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_340(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_341(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_342(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_343(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_344(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_345(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_346(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_347(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_348(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_349(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_350(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_351(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_352(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_353(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_354(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_355(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_356(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_357(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_358(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_359(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_360(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_361(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_362(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_363(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_364(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_365(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_366(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_367(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_368(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_369(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_370(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_371(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_372(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_373(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_374(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_375(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_376(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_377(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_378(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_379(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_380(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_381(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_382(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_383(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_384(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_385(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_386(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_387(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_388(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_389(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_390(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_391(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_392(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_393(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_394(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_395(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_396(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_397(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_398(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_399(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_400(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_401(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_402(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_403(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_404(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_405(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_406(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_407(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_408(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_409(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_410(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_411(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_412(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_413(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_414(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_415(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_416(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_417(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_418(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_419(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_420(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_421(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_422(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_423(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_424(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_425(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_426(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_427(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_428(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_429(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_430(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_431(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_432(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_433(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_434(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_435(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_436(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_437(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_438(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_439(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_440(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_441(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_442(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_443(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_444(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_445(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_446(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_447(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_448(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_449(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_450(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_451(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_452(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_453(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_454(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_455(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_456(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_457(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_458(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_459(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_460(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_461(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_462(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_463(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_464(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_465(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_466(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_467(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_468(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_469(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_470(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_471(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_472(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_473(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_474(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_475(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_476(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_477(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_478(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_479(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_480(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_481(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_482(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_483(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_484(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_485(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_486(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_487(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_488(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_489(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_490(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_491(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_492(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_493(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_494(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_495(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_496(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_497(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_498(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_499(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_500(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_501(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_502(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_503(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_504(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_505(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_506(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_507(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_508(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_509(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_510(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_511(p, o, s) 0
# define BOOST_PP_WHILE_CHECK_BOOST_PP_WHILE_512(p, o, s) 0
#
# endif
